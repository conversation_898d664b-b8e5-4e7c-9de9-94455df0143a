## Objective: Fix the Root Cause of the Unpredictable Habit Selection Bug

The primary goal is to resolve the critical bug in the habit selection logic. The current implementation causes unpredictable behavior when the user's "Start Day of Week" setting is changed, making the core functionality of the app unreliable.

## Root Cause Analysis

The bug originates in the **`HabitListView.java`** file, specifically within the **`bindCheckmark`** method.

The core of the problem is an incorrect calculation of the `day` variable. This integer, which is meant to represent the specific date of a habit completion, is calculated based on a fixed offset from the beginning of the year.

**This creates a critical flaw:**
When the "Start Day of Week" setting is changed (e.g., from Sunday to Monday), the entire date grid in the UI shifts visually. However, the underlying `day` calculation **does not adapt** to this visual shift.

This results in a **mismatch between the UI and the data layer**. The circle the user taps on the screen is no longer linked to the correct `day` value in the code. Consequently, the app records the completion for the wrong date, leading to the erratic and unpredictable selection behavior.

## Implementation Plan: Refactor the Date Calculation Logic

To permanently fix this bug, we must refactor the date calculation logic inside the `bindCheckmark` method in `HabitListView.java`.

### 1. Modify `bindCheckmark` to Use Relative Date Calculation

* **Goal:** The `day` variable must be calculated based on the dates that are **currently visible** on the screen, not a static offset from the start of the year.
* **Logic:**
    1.  Determine the start date of the visible week on the screen.
    2.  Calculate the `day` for each `Checkmark` (circle) by adding its column index to this visible start date.
    3.  This ensures that the `day` value is always in sync with what the user sees in the UI, regardless of the "Start Day of Week" setting.

### 2. Ensure Consistent Data Flow

* **Goal:** Verify that this newly calculated, correct `day` value is the one being passed to the presenter layer for database updates.
* **Action:** No changes should be needed in the presenter, but it is crucial to confirm that the corrected `day` variable from `bindCheckmark` flows through the existing `presenter.onHabitCheckmark()` call without modification.

### 3. Comprehensive Testing

* **Goal:** Confirm that the fix is robust and works in all scenarios.
* **Testing Requirement:** After implementation, rigorously test the habit selection functionality by:
    1.  Setting the "Start Day of Week" to **Sunday** and verifying that selections are accurate and isolated.
    2.  Setting the "Start Day of Week" to **Monday** and verifying that selections remain accurate and isolated.
    3.  Confirming that all percentage calculations update correctly after a selection is made in both modes.

---

## ⚙️ Mandatory Development Guidelines

These practices must be followed during all phases of development—planning, implementation, and review.

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.