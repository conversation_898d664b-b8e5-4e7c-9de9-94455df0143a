FAILURE: Build completed with 4 failures.



1: Task failed with an exception.

-----------

* What went wrong:

Execution failed for task ':app:mergeDebugResources'.

> java.io.IOException: Unable to delete directory 'E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out'

    Failed to delete some children. This might happen because a process has files open or has its working directory set in the target directory.

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\multi-v2

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\merged_res_blame_folder\debug\mergeDebugResources\out\single



* Try:

> Run with --stacktrace option to get the stack trace.

> Run with --info or --debug option to get more log output.

> Get more help at https://help.gradle.org.

==============================================================================



2: Task failed with an exception.

-----------

* What went wrong:

Execution failed for task ':app:packageDebugResources'.

> java.io.IOException: Unable to delete directory 'E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\incremental\debug\packageDebugResources'

    Failed to delete some children. This might happen because a process has files open or has its working directory set in the target directory.

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir\values

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir



* Try:

> Run with --stacktrace option to get the stack trace.

> Run with --info or --debug option to get more log output.

> Get more help at https://help.gradle.org.

==============================================================================



3: Task failed with an exception.

-----------

* What went wrong:

Execution failed for task ':app:mergeDebugNativeLibs'.

> java.nio.file.AccessDeniedException: E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\merged_native_libs\debug\mergeDebugNativeLibs\out\lib\arm64-v8a



* Try:

> Run with --stacktrace option to get the stack trace.

> Run with --info or --debug option to get more log output.

> Get more help at https://help.gradle.org.

==============================================================================



4: Task failed with an exception.

-----------

* What went wrong:

Execution failed for task ':app:mergeLibDexDebug'.

> java.io.IOException: Unable to delete directory 'E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug'

    Failed to delete some children. This might happen because a process has files open or has its working directory set in the target directory.

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\0

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\1

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\10

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\11

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\12

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\13

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\14

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\15

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\2

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\3

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\4

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\5

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\6

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\7

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\8

    - E:\Habit_Tracker_WorkSpace\UHabits_99\app\build\intermediates\dex\debug\mergeLibDexDebug\9

    - and more ...



* Try:

> Run with --stacktrace option to get the stack trace.

> Run with --info or --debug option to get more log output.

> Get more help at https://help.gradle.org.

==============================================================================



BUILD FAILED in 2m

24 actionable tasks: 24 executed